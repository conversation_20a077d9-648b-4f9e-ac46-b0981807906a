import { Message } from '@aws-sdk/client-sqs';
import { SQSService } from '@common/sqs/sqs.service';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { sanitizeMessageBody } from '@common/utils/message-sanitization.util';
import { logger } from '@edutalent/commons-sdk';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { OutgoingMessageUseCase } from '@message-hub/application/use-cases/outgoing-message.use-case';
import { IncomingMessageUseCase } from '@message-hub/application/use-cases/incoming-message.use-case';
import { CommunicationChannel } from '@common/enums';
import { randomUUID as uuidv4 } from 'crypto';

@Injectable()
export class SQSConsumerService implements OnModuleInit {
  constructor(
    private readonly sqsService: SQSService,
    private readonly outgoingMessageUseCase: OutgoingMessageUseCase,
    private readonly incomingMessageUseCase: IncomingMessageUseCase,
  ) {}

  onModuleInit() {
    logger.info('Starting SQS consumers for message_hub module...');

    const queueConsumerFunctionMap = new Map<string, (messages: Message[]) => void>();

    queueConsumerFunctionMap.set(
      process.env.OUTGOING_MESSAGE_QUEUE_URL,
      this.outGoingMessageConsumer.bind(this),
    );

    queueConsumerFunctionMap.set(
      process.env.INCOMING_MESSAGE_QUEUE_URL,
      this.incomingMessageConsumer.bind(this),
    );

    for (const [queueUrl, handler] of queueConsumerFunctionMap.entries()) {
      this.sqsService.createConsumer(queueUrl, 10, handler);
    }
  }

  private async outGoingMessageConsumer(messages: Message[]): Promise<void> {
    const batchCorrelationId = `batch-${uuidv4()}`;

    for (const message of messages) {
      const messageStartTime = Date.now();
      const messageCorrelationId = `msg-${uuidv4()}`;

      try {
        const { Body: body } = message;
        const sendMessageRequestDto = JSON.parse(body);

        await CorrelationContextService.run(
          {
            traceId: messageCorrelationId,
            layer: 'SQS_CONSUMER',
            operation: 'consumeOutgoingMessage',
          },
          async () => {
            await this.outgoingMessageUseCase.consumeMessage(sendMessageRequestDto);
          },
        );
      } catch (error) {
        const messageDuration = Date.now() - messageStartTime;
        logger.error('Error processing outgoing messages', {
          traceId: messageCorrelationId,
          batchCorrelationId,
          error: JSON.stringify(error),
          operation: 'outGoingMessageConsumer',
          duration: `${messageDuration}ms`,
          layer: 'SQS_CONSUMER',
        });
      }
    }
  }

  private async incomingMessageConsumer(messages: Message[]): Promise<void> {
    for (const message of messages) {
      const messageCorrelationId = `msg-${uuidv4()}`;

      try {
        const { Body: body } = message;
        const incomingMessageDto = JSON.parse(body);

        const communicationChannelEnum =
          CommunicationChannel[incomingMessageDto.communicationChannel];

        // Use phone number as part of traceId for better business correlation
        const phoneBasedTraceId = `phone-${incomingMessageDto.from}-${messageCorrelationId}`;

        await CorrelationContextService.run(
          {
            traceId: phoneBasedTraceId,
            layer: 'SQS_CONSUMER',
            operation: 'consumeIncomingMessage',
          },
          async () => {
            await this.incomingMessageUseCase.consumeIncomingMessage(
              incomingMessageDto.from,
              incomingMessageDto.to,
              incomingMessageDto.message,
              incomingMessageDto.messageType,
              communicationChannelEnum,
              incomingMessageDto.fileUrl,
            );
          },
        );
      } catch (error) {
        logger.error('Error processing incoming messages', {
          correlationId: messageCorrelationId,
          error: JSON.stringify(error),
          layer: 'SQS_CONSUMER',
        });
      }
    }
  }
}
