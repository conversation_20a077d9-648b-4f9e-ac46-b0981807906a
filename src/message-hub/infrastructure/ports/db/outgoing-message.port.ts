import { DbCommonPort } from '@common/db/ports/common.port';
import { OutgoingMessageEntity } from '@message-hub/domain/entities/outgoing-message.entity';
import { CommunicationChannel } from '@common/enums';

export interface OutgoingMessagePort extends DbCommonPort<OutgoingMessageEntity> {
  insertOutgoingMessage(
    {
      customerId,
      from,
      to,
      messageType,
      message,
      channel,
      status,
      isFirstMessage,
      apiUrl,
      fileUrl,
    }: {
      customerId: string;
      from: string;
      to: string;
      messageType: string;
      message: string;
      channel: string;
      status: string;
      isFirstMessage: boolean;
      apiUrl: string;
      fileUrl?: string;
    },
    randomDelay: number,
  ): Promise<void>;

  processOutgoingMessage(
    fromNumber: string,
    channel: CommunicationChannel,
    sendMessage: (to: string, message: string, apiUrl: string) => Promise<void>,
    sendMessageWithFile: (
      to: string,
      message: string,
      apiUrl: string,
      fileUrl: string,
      fileType: string,
    ) => Promise<void>,
    sendSMS: (to: string, message: string, apiUrl: string) => Promise<void>,
  ): Promise<void>;

  getPendingOutgoingMessage(
    customerId: string,
    channel: CommunicationChannel,
    to: string,
  ): Promise<OutgoingMessageEntity[]>;

  getTimestampFromDatabase(): Promise<{ now: Date; nowToString: string }>;

  getTotalFirstMessagesSentByPortfolio(
    customerId: string,
    portfolioId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<{ date: string; count: number; total: number }[]>;
}
